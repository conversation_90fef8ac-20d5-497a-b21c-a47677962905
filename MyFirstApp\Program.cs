string age;
age = Console.ReadLine();

Console.WriteLine("Your age is " + age);
//let change the string to int
int age1 = Convert.ToInt32(age);
Console.WriteLine("Your age is " + age1);
//lets learn about the try catch block
try
{
    int age2 = Convert.ToInt32(age);
    Console.WriteLine("Your age is " + age2);
}
catch (Exception e)
{
    Console.WriteLine("Error: " + e.Message);
}
//lets learn about the try catch finally block
try
{
    int age3 = Convert.ToInt32(age);
    Console.WriteLine("Your age is " + age3);
}
catch (Exception e)
{
    Console.WriteLine("Error: " + e.Message);
}
finally
{
    Console.WriteLine("This is the finally block");
}